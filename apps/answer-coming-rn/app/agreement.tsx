import { StateView } from '@jgl/biz-components';
import {
  answerComingAppAgreementLinks as agreementLinks,
  agreementStateAtom,
  openExternalLink,
  useAgreementScreenIsModal,
} from '@jgl/biz-func';
import { envVars } from '@jgl/utils';
import { useRouter } from 'expo-router';
import { useAtomValue } from 'jotai';
import { Suspense, useCallback, useEffect, useMemo, useRef } from 'react';
import { SafeAreaView, StyleSheet, Image, ScrollView } from 'react-native';
// import { Button, Spinner, Text, XStack } from 'tamagui';
import { StatusBarForModal } from '../components/StatusBarForModal';
import { useSetAgreementState } from '../hooks/useAgreementState';
import {
  YTButton,
  YTScrollView,
  YTSpinner,
  YTText,
  YTXStack,
  YTYStack,
} from '@bookln/cross-platform-components';

export default function AgreementScreen() {
  const { externalLinks, handlePressAgree, handlePressDisagree } =
    useAgreementScreen();
  const appName = envVars.appNameForUser();
  const agreementState = useAtomValue(agreementStateAtom);

  const buttonDisabled = useMemo(() => {
    return (
      agreementState === 'isAgreeing' || agreementState === 'isDisagreeing'
    );
  }, [agreementState]);

  const agreementScreenIsModal = useAgreementScreenIsModal();

  return (
    <>
      {agreementScreenIsModal ? <StatusBarForModal /> : null}
      <SafeAreaView className='flex-1 bg-white'>
        <Suspense fallback={<StateView isLoading />}>
          <YTXStack paddingHorizontal={24} marginTop={40} marginBottom={18}>
            <YTXStack style={styles.relative}>
              <Image
                source={require('../assets/images/Logo.png')}
                style={styles.logoImage}
              />

              <YTText fontSize={20} fontWeight='bold' lineHeight={28}>
                欢迎使用
                {'\n'}
                {appName}APP
              </YTText>
            </YTXStack>
          </YTXStack>
          <YTScrollView
            flex={1}
            contentInset={{ bottom: 20 }}
            contentContainerStyle={styles.scrollContentContainer}
          >
            <YTText mx={24} fontSize={14} color={'$gray11'} marginBottom={40}>
              我们非常重视您的个人信息及隐私保护，为了更好地保障您的个人权益，在您使用
              {appName}服务前，请务必认真阅读
              {externalLinks.map((e, i) => {
                const isLastOne = i === externalLinks.length - 1;
                return (
                  <>
                    <ExternalLink key={e.url} externalUrl={e} />
                    {isLastOne ? null : <YTText>、</YTText>}
                  </>
                );
              })}
              的全部条款，以便您了解我们如何向您提供服务、保障您的合法权益，如何收集、使用、储存、共享您的相关个人信息，如何管理您的相关个人信息，以及我们对您提供的相关信息的保护方式等。我们会严格在您的授权范围内，按照上述协议约定的方式收集、使用、储存、共享您的账户信息、日志信息等。
            </YTText>
          </YTScrollView>

          <YTYStack gap={8} m={20}>
            <YTButton
              size='large'
              fontSize={16}
              borderRadius={10}
              themeColor={'yellow'}
              disabled={buttonDisabled}
              onPress={handlePressAgree}
            >
              同意并继续
            </YTButton>
            <YTButton
              size='large'
              type='text'
              borderRadius={10}
              backgroundColor={'white'}
              color={'$color11'}
              marginBottom={16}
              disabled={buttonDisabled}
              onPress={handlePressDisagree}
            >
              {agreementState === 'isDisagreeing' ? <YTSpinner /> : '不同意'}
            </YTButton>
          </YTYStack>
        </Suspense>
      </SafeAreaView>
    </>
  );
}

type ExternalLinkProps = {
  externalUrl: { name: string; url: string };
};

const ExternalLink = (props: ExternalLinkProps) => {
  const { externalUrl } = props;
  const { name, url } = externalUrl;

  return (
    <YTText
      fontWeight={'bold'}
      color={'$gray12'}
      fontSize={14}
      onPress={() => openExternalLink(url)}
    >
      《{name}》
    </YTText>
  );
};

const useAgreementScreen = () => {
  const { agree, disagree } = useSetAgreementState();
  const agreementState = useAtomValue(agreementStateAtom);
  const shouldDismissModal = useRef<boolean>(false);
  const currentRouter = useRouter();

  // 当状态变为 disagreed 的时候直接进入了首页
  // 再次打开协议页面是 modal 状态
  // 需要在同意协议后 dismiss modal
  useEffect(() => {
    if (agreementState === 'disagreed') {
      shouldDismissModal.current = true;
    }

    if (agreementState === 'agreed') {
      if (shouldDismissModal.current === true) {
        if (currentRouter.canGoBack()) {
          currentRouter.back();
        }
      }
    }
  }, [agreementState, currentRouter]);

  const handlePressAgree = useCallback(async () => {
    await agree();
  }, [agree]);

  return {
    externalLinks: agreementLinks,
    handlePressAgree,
    handlePressDisagree: disagree,
  };
};

const styles = StyleSheet.create({
  buttonContainer: { marginHorizontal: 6, flex: 1 },
  scrollContentContainer: { alignItems: 'center' },
  relative: { position: 'relative', marginTop: 40 },
  logoImage: {
    width: 60,
    height: 60,
    marginRight: 12,
    marginBottom: 8,
  },
});
