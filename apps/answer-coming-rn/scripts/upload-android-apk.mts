#! /usr/bin/env zx
import OSS from 'ali-oss';
import axios from 'axios';
import { existsSync } from 'fs';
import { readdir, readFile, stat } from 'fs/promises';
import path, { dirname } from 'path';
import { exit } from 'process';
import QRCode from 'qrcode';
import { fileURLToPath } from 'url';
import { $, echo } from 'zx';
import { formatCurrentDateTimeForAndroid } from './build-common.mts';
import { ossUploadTimeout } from './common.mts';
import { getGitCommit } from './git-util.mts';

const [
  nodePath,
  scriptPath,
  appId,
  host,
  devBuild,
  expoUpdateChannel,
  ...restArgs
] = process.argv;

// https://github.com/google/zx/issues/126#issuecomment-850621670
// @ts-ignore
$.noquote = async (...args) => {
  const q = $.quote;
  $.quote = (v) => v;
  // @ts-ignore
  const p = $(...args);
  await p;
  $.quote = q;
  return p;
};

if (!appId) {
  echo(`appId - ${appId} 有误`);
  exit(1);
}
if (!host) {
  echo(`host - ${host} 有误`);
  exit(1);
}
if (!devBuild) {
  echo(`devBuild - ${devBuild} 有误`);
  exit(1);
}

echo('process.argv', process.argv);

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const ossConfigFileName = 'oss_config.json';

const apkFileRelativePath = `./app/build/outputs/apk/${
  devBuild === 'true' ? 'debug' : 'release'
}/${devBuild === 'true' ? 'app-debug' : 'app-release'}.apk`;
const apkFileAbsolutePath = path.join(
  __dirname,
  '..',
  'android',
  apkFileRelativePath,
);
const channelApkDirRelativePath = './app/build/channel/';
const channelApkDirAbsolutePath = path.join(
  __dirname,
  '..',
  'android',
  channelApkDirRelativePath,
);

const androidDirPath = path.join(__dirname, '..', 'android');

type OSSConfig = {
  accessKeyId: string;
  accessKeySecret: string;
  bucket: string;
  cdnHost: string;
  endpoint: string;
  region: string;
  token: string;
  uploadDir: string;
};

const replaceAll = (str: string, find: string, replace: string) => {
  return str.replace(new RegExp(find, 'g'), replace);
};

/**
 * 检查待上传文件和文件夹
 */
const checkWaitUploadFilePath = async () => {
  if (host === 'daily') {
    if (!existsSync(apkFileAbsolutePath)) {
      echo(`${apkFileAbsolutePath} 路径不存在，请检查 Android 打包流程`);
      exit(1);
    }
  } else if (host === 'release') {
    // 检查渠道包目录是否存在
    if (!existsSync(channelApkDirAbsolutePath)) {
      echo(
        `${channelApkDirAbsolutePath} 不存在，请检查 Android 渠道包打包流程`,
      );
      exit(1);
    } else {
      const stats = await stat(channelApkDirAbsolutePath);
      if (!stats.isDirectory()) {
        echo(
          `${channelApkDirAbsolutePath} 不是一个目录，请检查 Android 渠道包打包流程`,
        );
        exit(1);
      }
    }
  }
};

/**
 * 获取 App 版本号
 * @param appId AppId
 */
async function getAppVersion(appId: string): Promise<string | undefined> {
  const url = `http://app-prepub.bookln.cn/appservice/miniappinfo.do?appid=${appId}&type=2`;

  try {
    const response = await axios.post(url);

    if (response.status === 200) {
      const version = response.data.data.miniAPPVersion;
      return version;
    }
  } catch (error) {
    console.error(`Error fetching app version: ${error.message}`);
  }

  return undefined;
}

/**
 * 上传测试包/开发包
 * @param param 参数
 */
const uploadDailyBuildApk = async (param: {
  uploadDirPath: string;
  ossClient: OSS;
  cdnHost: string;
}) => {
  const { uploadDirPath, ossClient, cdnHost } = param;
  // 1.上传 APK
  let apkCDNUrl = '';
  if (devBuild === 'true') {
    apkCDNUrl = `${uploadDirPath}${host}/${appId}_dev_build_${formatCurrentDateTimeForAndroid()}.apk`;
  } else {
    apkCDNUrl = `${uploadDirPath}${host}/${appId}_${expoUpdateChannel}_${formatCurrentDateTimeForAndroid()}.apk`;
  }
  echo(`${apkFileAbsolutePath} 文件上传路径 ${apkCDNUrl}`);
  const apkBuffer = await readFile(apkFileAbsolutePath);
  const apkUploadResult = await ossClient.put(apkCDNUrl, apkBuffer, {
    timeout: ossUploadTimeout,
  });
  if (apkUploadResult.res.status === 200) {
    echo(
      `${apkFileAbsolutePath} 文件上传成功，CDN 地址为 ${apkUploadResult.url}`,
    );
  } else {
    echo(`${apkFileAbsolutePath} 文件上传失败`, apkUploadResult.res.toString());
    exit(1);
  }

  // 2.上传二维码
  const qrCodePath = `${androidDirPath}/qr_code.jpg`;
  // 2.1 先合成二维码
  const downloadAddress = `https://${cdnHost}/${apkCDNUrl}`;
  QRCode.toFile(qrCodePath, downloadAddress, async (err) => {
    if (err) {
      echo(`${qrCodePath} 合成二维码失败`, err.toString());
      exit(1);
    }

    // 2.2 再上传二维码
    const qrCDNUrl = `${uploadDirPath}qr_img/${appId}_${formatCurrentDateTimeForAndroid()}.jpg`;
    echo(`${qrCodePath} 文件上传路径 ${qrCDNUrl}`);
    const qrCodeBuffer = await readFile(qrCodePath);
    const qrCodeUploadResult = await ossClient.put(qrCDNUrl, qrCodeBuffer);
    if (qrCodeUploadResult.res.status === 200) {
      echo(`${qrCodePath} 文件上传成功，CDN 地址为 ${qrCodeUploadResult.url}`);
    } else {
      echo(`${qrCodePath} 文件上传失败`, qrCodeUploadResult.res.toString());
      exit(1);
    }

    // 获取 App 版本
    const appVersion = await getAppVersion(appId);
    const gitCommit = await getGitCommit();
    const qrCodeImageUrl = `https://${cdnHost}/${qrCDNUrl}`;
    // @ts-ignore
    await $.noquote`npx tsx ./notify-build-msg.mts appId=${appId} platform=android buildType=${
      devBuild === 'true' ? 'devBuild' : 'normalBuild'
    } qrCodeImageUrl=${qrCodeImageUrl} messageTitle="答案来了 Android ${appVersion}" downloadAddress="地址：${downloadAddress}" taskName="任务：${
      process.env.JOB_NAME
    }" buildGitBranch="分支：${
      process.env.GIT_BRANCH
    }" buildGitHash="代码：${gitCommit}"`;
  });
};

/**
 * 更新 App 版本号
 */
const updateAppVersion = async (param: {
  appId: string;
  channel: string;
  gitCommit: string;
  channelApkCDNUrl: string;
}): Promise<void | {
  downloadUrl: string | undefined;
  previewVersion: string | undefined;
}> => {
  const { appId, channel, gitCommit, channelApkCDNUrl } = param;
  const requestUri = `https://prepub.bookln.cn/appservice/newappversion.do`;
  const requestUrl = `${requestUri}?appid=${appId}&type=2&status=1&apkurl=${channelApkCDNUrl}&channel=${channel}&hash=${gitCommit}`;
  // TODO: cenfeng - 直接通过 npx tsx 运行当前脚本会抛出 SyntaxError: The requested module 'lodash' does not provide an export named 'cloneDeep'，因为 appendQuery 所属的 @yunti-private/utils 底层依赖了 lodash
  // const requestUrl = appendQuery(requestUri, {
  //   appid: appId,
  //   type: 2,
  //   status: 1,
  //   apkurl: apkUrl,
  //   channel,
  //   hash: gitCommit,
  // });
  // const requestUrl = `${requestUri}?appid=${appId}&type=2&status=1&apkurl=${apkUrl}&channel=${channel}&hash=${gitCommit}`;
  echo(`newappversion.do requestUrl - ${requestUrl}`);
  const response = await axios.get(requestUrl);
  if (response.status === 200) {
    const responseData = response.data.data;
    const downloadUrl = responseData?.['preivewResult'];
    const previewVersion = responseData?.['previewVersion'];
    return {
      downloadUrl,
      previewVersion,
    };
  } else {
    echo('Android 渠道包任务调用 newappversion.do 更新版本失败', response.data);
    exit(1);
  }
};

/**
 * 上传渠道包
 * @param param 参数
 */
const uploadReleaseBuildApk = async (param: {
  uploadDirPath: string;
  ossClient: OSS;
  cdnHost: string;
}) => {
  const { uploadDirPath, ossClient, cdnHost } = param;
  // channelApkDirAbsolutePath
  try {
    // 读取渠道包目录下所有的 APK
    const channelApkFiles = await readdir(channelApkDirAbsolutePath);
    const downloadUrls: string[] = [];
    let previewVersion = '';
    const gitCommit = await getGitCommit();
    for await (const channelApkFileName of channelApkFiles) {
      // 渠道包 APK 绝对路径
      const channelApkAbsolutePath = path.join(
        channelApkDirAbsolutePath,
        channelApkFileName,
      );
      // 检测是否是一个文件
      const stats = await stat(channelApkAbsolutePath);
      if (stats.isFile()) {
        // 从文件名中取出版本号和渠道
        const regularResult = channelApkFileName.match(/_([^_]*)_/g);
        if (regularResult && regularResult.length === 2) {
          // 版本号
          const version = replaceAll(regularResult?.[0] ?? '', '_', '');
          echo(`版本号 - ${version}`);
          // 渠道
          const channel = replaceAll(regularResult?.[1] ?? '', '_', '');
          echo(`渠道 - ${channel}`);
          // APK 上传 url
          const channelApkUploadUrl = `${uploadDirPath}${host}/${appId}_${version}_${channel}_${expoUpdateChannel}_${formatCurrentDateTimeForAndroid()}.apk`;
          echo(
            `渠道包本地路径 ${channelApkAbsolutePath} 文件上传路径 ${channelApkUploadUrl}`,
          );
          const channelApkBuffer = await readFile(channelApkAbsolutePath);
          const channelApkUploadResult = await ossClient.put(
            channelApkUploadUrl,
            channelApkBuffer,
            {
              timeout: ossUploadTimeout,
            },
          );
          if (channelApkUploadResult.res.status === 200) {
            const apkDownloadUrl = `https://${cdnHost}/${channelApkUploadUrl}`;
            echo(
              `${channelApkAbsolutePath} 文件上传成功，CDN 地址为 ${apkDownloadUrl}`,
            );
            const result = await updateAppVersion({
              appId,
              channel,
              gitCommit,
              channelApkCDNUrl: apkDownloadUrl,
            });
            if (result) {
              downloadUrls.push(apkDownloadUrl ?? '');
              previewVersion = result.previewVersion ?? '';
            }
          } else {
            echo(
              `${apkFileAbsolutePath} 文件上传失败`,
              channelApkUploadResult.res.toString(),
            );
            exit(1);
          }
        } else {
          echo(`从 ${channelApkFileName} 解析版本和渠道失败`);
          exit(1);
        }
      } else {
        echo(`读取 ${channelApkAbsolutePath} 失败`);
        exit(1);
      }
    }

    const apkCounts = downloadUrls.length.toString();
    const downloadStr = downloadUrls.join('\n');
    // @ts-ignore
    await $.noquote`npx tsx ./notify-build-msg.mts appId=${appId} platform=android buildType=normalBuild qrCodeImageUrl="" messageTitle="答案来了 Android ${previewVersion} 渠道包" downloadAddress="渠道数量：${apkCounts}" taskName="任务：${process.env.JOB_NAME}" buildGitBranch="分支：${process.env.GIT_BRANCH}" buildGitHash="代码：${gitCommit}" androidChannelDownloadAddress="地址：\n${downloadStr}"`;
  } catch (error) {
    echo(`uploadReleaseBuildApk 失败：`, error.toString());
    exit(1);
  }
};

/**
 * 使用 OSS 进行上传
 * @param jsonData OSS 配置
 */
const uploadWithOSS = async (jsonData: OSSConfig) => {
  const ossClient = new OSS({
    region: jsonData.region,
    accessKeyId: jsonData.accessKeyId,
    accessKeySecret: jsonData.accessKeySecret,
    stsToken: jsonData.token,
    bucket: jsonData.bucket,
  });

  const uploadDirPath = `${jsonData.uploadDir}android/`;

  if (host === 'daily') {
    uploadDailyBuildApk({
      uploadDirPath,
      ossClient,
      cdnHost: jsonData.cdnHost,
    });
  } else if (host === 'release') {
    uploadReleaseBuildApk({
      uploadDirPath,
      ossClient,
      cdnHost: jsonData.cdnHost,
    });
  }
};

/**
 * 解析 OSS 配置文件
 * @param ossConfigFilePath OSS 配置文件路径
 */
const parseOSSConfig = async (ossConfigFilePath: string) => {
  const data = await readFile(ossConfigFilePath, 'utf-8');

  try {
    const jsonData = JSON.parse(data);
    echo('OSS 配置文件内容', data);

    await checkWaitUploadFilePath();

    await uploadWithOSS(jsonData);
  } catch (error) {
    echo('解析 OSS 配置文件失败', error.toString());
  }
};

const run = async () => {
  // 1.定位到 oss_config.json
  const ossConfigFilePath = path.join(__dirname, '..', ossConfigFileName);

  echo('ossConfigFilePath', ossConfigFilePath);
  echo('__filename', __filename);
  echo('__dirname', __dirname);

  // 2.检查 ossConfigFilePath 是否存在
  if (existsSync(ossConfigFilePath)) {
    // 如果存在，则解析 oss 配置文件
    parseOSSConfig(ossConfigFilePath);
  } else {
    echo(`${ossConfigFileName} 文件未找到，请检查 getOSS.mts 脚本是否执行成功`);
  }
};

run();
