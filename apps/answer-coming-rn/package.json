{"name": "@dall-app/dall-rn", "main": "expo-router/entry", "version": "1.0.2", "private": true, "nx": {"tags": ["scope:apps"]}, "scripts": {"start": "expo start", "start:clear": "expo start --clear", "android": "expo run:android", "ios": "expo run:ios", "pod": "(cd ios && bundle install && bundle exec pod install --verbose)", "pod:c": "(cd ios && bundle install && bundle exec pod deintegrate && bundle exec pod repo update --verbose && bundle exec pod install)", "web": "expo start --web", "xcode": "open ios/dall.xcworkspace", "as": "open -a /Applications/Android' 'Studio.app android", "test": "jest --watchAll", "build:ios": "npx tsx scripts/build-ios.mts", "build:ios:dev": "npx tsx scripts/build-ios-dev.mts", "build:ios:upload": "npx tsx scripts/build-ios-upload.mts", "build:android": "npx tsx scripts/build-android.mts", "build:android:dev": "npx tsx scripts/build-android-dev.mts", "build:android:release": "npx tsx scripts/build-android.mts --isAndroidChannelRelease=true", "build:android:channel": "npx tsx scripts/build-android-channel.mts", "bundlejs:ios": "npx react-native bundle --entry-file='node_modules/expo-router/entry.js' --bundle-output='./ios/main.jsbundle' --dev=false --platform='ios' --assets-dest='./ios'", "bundlejs:android": "npx react-native bundle --entry-file='node_modules/expo-router/entry.js' --bundle-output='./android/main.jsbundle' --dev=false --platform='android' --assets-dest='./android'", "ios:switch": "cd ios && bundle exec fastlane switch_app app_id:94 is_inhouse:false is_dev:false && cd .."}, "jest": {"preset": "jest-expo"}, "dependencies": {"@bam.tech/react-native-image-resizer": "3.0.11", "@expo/react-native-action-sheet": "4.1.1", "@gorhom/bottom-sheet": "5.1.6", "@jgl/biz-components": "workspace:*", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/hooks": "100.1.0", "@react-native-community/netinfo": "11.4.1", "@react-native-voice/voice": "3.2.4", "@react-navigation/native": "7.1.10", "@sentry/react-native": "6.16.0", "@shopify/flash-list": "1.7.6", "@shopify/react-native-skia": "2.0.0-next.4", "@sleiv/react-native-graceful-exit": "0.1.0", "@yunti-private/logger-rn": "3.0.3", "@yunti-private/rn-expo-updates-helper": "0.1.16", "@yunti-private/rn-qq": "0.0.2", "@yunti-private/rn-suntone": "0.0.8", "@yunti-private/rn-umeng": "1.0.16", "@yunti/react-native-chivox": "https://github.com/yuntitech/react-native-chivox#405cbd3857de65d5bcbfd53d238734e3237c5277", "expo": "53.0.20", "expo-audio": "0.4.8", "expo-blur": "14.1.5", "expo-build-properties": "0.14.8", "expo-camera": "16.1.11", "expo-clipboard": "7.1.5", "expo-constants": "17.1.7", "expo-dev-client": "5.2.4", "expo-device": "7.1.4", "expo-font": "13.3.2", "expo-haptics": "14.1.4", "expo-image": "2.4.0", "expo-image-manipulator": "13.1.7", "expo-image-picker": "16.1.4", "expo-linear-gradient": "14.1.5", "expo-linking": "7.1.7", "expo-localization": "16.1.6", "expo-media-library": "17.1.7", "expo-router": "5.1.4", "expo-screen-orientation": "8.1.7", "expo-splash-screen": "0.30.10", "expo-status-bar": "2.2.3", "expo-system-ui": "5.0.10", "expo-tracking-transparency": "5.2.4", "expo-updates": "0.28.14", "expo-web-browser": "14.2.0", "lottie-react-native": "7.2.2", "native-wechat": "git+https://git.bookln.cn/github/native-wechat.git#0a8bf80d7b7fd40ea6f5f0dd8c35dcfe2a2f6449", "react-native": "0.79.5", "react-native-device-info": "10.12.1", "react-native-fs": "2.20.0", "react-native-gesture-handler": "2.24.0", "react-native-haptic-feedback": "2.3.3", "react-native-iap": "12.16.2", "react-native-image-colors": "2.5.0", "react-native-image-viewing": "0.2.2", "react-native-image-zoom-viewer": "3.0.1", "react-native-keyboard-controller": "1.17.5", "react-native-linear-gradient": "2.8.3", "react-native-marked": "6.0.7", "react-native-math-view": "3.9.5", "react-native-modal": "14.0.0-rc.1", "react-native-permissions": "5.4.1", "react-native-popover-view": "6.1.0", "react-native-progress": "5.0.1", "react-native-reanimated": "3.17.5", "react-native-reanimated-carousel": "4.0.2", "react-native-reanimated-viewer": "1.6.0", "react-native-render-html": "6.3.4", "react-native-root-siblings": "5.0.1", "react-native-root-toast": "3.5.1", "react-native-safe-area-context": "5.4.0", "react-native-screenguard": "1.1.0", "react-native-screens": "4.11.1", "react-native-share": "12.0.11", "react-native-sound": "https://github.com/yuntitech/react-native-sound#11581dc4794ba43957ce90da2df9814ed24d8578", "react-native-svg": "15.11.2", "react-native-swiper": "1.6.0", "react-native-table-component": "1.2.2", "react-native-toast-hybrid": "2.6.0", "react-native-user-agent": "https://github.com/yuntitech/react-native-user-agent.git", "react-native-view-shot": "4.0.3", "react-native-vision-camera": "4.7.0", "react-native-webview": "13.15.0", "react-native-zoom-toolkit": "5.0.1", "react-syntax-highlighter": "15.6.1", "rn-alioss": "0.2.5"}, "devDependencies": {"@expo/metro-config": "0.20.14", "@expo/metro-runtime": "5.0.4", "@tamagui/babel-plugin": "1.129.11", "@tamagui/cli": "1.129.11", "@tamagui/metro-plugin": "1.129.11", "@types/marked": "5.0.0", "@types/react": "19.0.14", "chokidar": "4.0.3", "jest": "29.7.0", "jest-expo": "53.0.9", "metro": "0.82.4", "typescript": "5.8.3"}, "tamagui": {"config": "./tamagui.config.ts"}}