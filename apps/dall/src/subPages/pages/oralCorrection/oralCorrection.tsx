import { AiTakePhoto } from '@jgl/biz-components';
import { NavigationBar, WithLogin } from '@jgl/components';
import { container } from '@jgl/container';
import { uploadToOSSWith15DaysExpiry } from '@jgl/upload';
import { showToast } from '@jgl/utils';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { INetworking } from '@yunti-private/net';
import { useCallback } from 'react';
import { cameraCorrect, get15DayFileUploadInfoFor } from 'src/api-dto/api';
import routerMap from 'src/routerMap';
import { handleJumpSubPages } from 'src/utils';

/** 口算批改 */
const OralCorrectionScreen = () => {
  const uploadFetch = async (net: INetworking) => {
    const res = await net.fetch(get15DayFileUploadInfoFor());
    return res.data;
  };

  const onUploadImage = useCallback(async (imageUrl: string) => {
    const url = await uploadToOSSWith15DaysExpiry({
      tmpPath: imageUrl,
      options: {
        net: container.net(),
      },
      bizCode: 'ac',
    });

    return url;
  }, []);

  const getRecordId = useCallback(
    async (url: string) => {
      Taro.showLoading({
        title: '正在批改中…',
        mask: true,
      });
      const imageUrl = await onUploadImage(url);
      if (!imageUrl) {
        Taro.hideLoading();
        return;
      }
      const { success, data, msg } = await container
        .net()
        .fetch(
          cameraCorrect({
            url: imageUrl,
          }),
        )
        .finally(() => {
          Taro.hideLoading();
        });
      if (success && data) {
        handleJumpSubPages(
          `${routerMap.oralCorrectionResult}?recordId=${data.recordId}`,
        );
      } else {
        msg &&
          showToast({
            title: msg,
          });
      }
    },
    [onUploadImage],
  );

  const onCommitPhoto = useCallback(
    (url: string) => {
      getRecordId(url);
    },
    [getRecordId],
  );

  return (
    <View className='flex h-full w-full flex-col'>
      <NavigationBar title='口算批改' back type='left' />

      <AiTakePhoto
        visible
        horizontalLine
        photoTips='题目与参考线平行'
        topTitle='一次拍摄一整页'
        onCommitPhoto={onCommitPhoto}
      />
    </View>
  );
};

export default WithLogin(OralCorrectionScreen);
